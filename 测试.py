import os

# Fix for xtquant DLL loading issue
def fix_xtquant_dll():
    """Fix DLL loading for xtquant"""
    try:
        import xtquant
        xtquant_path = os.path.dirname(xtquant.__file__)
    except ImportError:
        # Fallback path
        xtquant_path = r"D:\anacoda\envs\Fuel\lib\site-packages\xtquant"

    # Add to PATH
    current_path = os.environ.get('PATH', '')
    if xtquant_path not in current_path:
        os.environ['PATH'] = xtquant_path + os.pathsep + current_path

    # Add to DLL search path (Python 3.8+)
    if hasattr(os, 'add_dll_directory'):
        try:
            os.add_dll_directory(xtquant_path)
        except:
            pass

# Apply the fix
fix_xtquant_dll()

# Your original code
try:
    from xtquant import xtdata

    code_list = ['000001.SZ', '600000.SH', '430017.BJ']
    full_tick = xtdata.get_full_tick(code_list)
    print(full_tick)

except ImportError as e:
    print(f"ImportError: {e}")
    print("\nTo fix this issue, please:")
    print("1. Run: python install_vcredist.py")
    print("2. Or manually install Visual C++ Redistributable 2015-2019 x64")
    print("3. Restart your computer")
    print("4. Try running this script again")

except Exception as e:
    print(f"Error: {e}")
    print("This might be normal if the market data service is not running")
