#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
xtquant DLL loading fix
This script addresses the common DLL loading issue with xtquant library
"""

import sys
import os

def fix_xtquant_dll_path():
    """
    Fix the DLL loading issue for xtquant by adding the package directory to PATH
    and using os.add_dll_directory for Python 3.8+
    """
    # Get the xtquant package directory
    try:
        import xtquant
        xtquant_dir = os.path.dirname(xtquant.__file__)
        print(f"Found xtquant at: {xtquant_dir}")
    except ImportError:
        # Fallback to expected location
        python_path = sys.executable
        site_packages = os.path.join(os.path.dirname(python_path), 'lib', 'site-packages')
        xtquant_dir = os.path.join(site_packages, 'xtquant')
        print(f"Using expected xtquant location: {xtquant_dir}")
    
    if not os.path.exists(xtquant_dir):
        raise FileNotFoundError(f"xtquant directory not found: {xtquant_dir}")
    
    # Method 1: Add to PATH environment variable
    current_path = os.environ.get('PATH', '')
    if xtquant_dir not in current_path:
        os.environ['PATH'] = xtquant_dir + os.pathsep + current_path
        print(f"✓ Added {xtquant_dir} to PATH")
    
    # Method 2: Use os.add_dll_directory (Python 3.8+)
    if hasattr(os, 'add_dll_directory'):
        try:
            os.add_dll_directory(xtquant_dir)
            print(f"✓ Added {xtquant_dir} to DLL search directories")
        except Exception as e:
            print(f"⚠ Warning: Could not add DLL directory: {e}")
    
    return xtquant_dir

def test_xtquant():
    """Test xtquant functionality"""
    try:
        print("\n" + "="*50)
        print("Testing xtquant import and functionality...")
        print("="*50)
        
        # Fix DLL path first
        xtquant_dir = fix_xtquant_dll_path()
        
        # Import xtdata
        from xtquant import xtdata
        print("✓ Successfully imported xtdata")
        
        # Test basic functionality
        print("\nTesting get_full_tick function...")
        code_list = ['000001.SZ', '600000.SH', '430017.BJ']
        
        try:
            full_tick = xtdata.get_full_tick(code_list)
            print("✓ Successfully called get_full_tick")
            print(f"Result type: {type(full_tick)}")
            if full_tick:
                print(f"Result: {full_tick}")
            else:
                print("Result: No data returned (this is normal if market is closed)")
        except Exception as e:
            print(f"⚠ get_full_tick failed: {e}")
            print("This might be normal if the market data service is not running")
        
        print("\n✓ xtquant is working correctly!")
        return True
        
    except ImportError as e:
        print(f"\n✗ Import Error: {e}")
        print("\nPossible solutions:")
        print("1. Install Visual C++ Redistributable 2015-2019")
        print("2. Reinstall xtquant package")
        print("3. Check if you're using the correct Python architecture (64-bit)")
        return False
        
    except Exception as e:
        print(f"\n✗ Unexpected Error: {e}")
        return False

if __name__ == "__main__":
    print("xtquant DLL Fix and Test Script")
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    print(f"Current working directory: {os.getcwd()}")
    
    success = test_xtquant()
    
    if success:
        print("\n" + "="*50)
        print("SUCCESS: xtquant is working!")
        print("You can now use xtquant in your scripts.")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("FAILED: xtquant is not working properly.")
        print("Please check the error messages above.")
        print("="*50)
