import sys
import os
import ctypes
from ctypes import wintypes

# Add the xtquant directory to PATH and DLL search path
xtquant_path = r"D:\anacoda\envs\Fuel\lib\site-packages\xtquant"

# Method 1: Add to PATH
original_path = os.environ.get('PATH', '')
os.environ['PATH'] = xtquant_path + os.pathsep + original_path
print(f"Added {xtquant_path} to PATH")

# Method 2: Add to DLL search path (Python 3.8+)
if hasattr(os, 'add_dll_directory'):
    try:
        os.add_dll_directory(xtquant_path)
        print(f"Added {xtquant_path} to DLL search path")
    except Exception as e:
        print(f"Failed to add DLL directory: {e}")

# Method 3: Change working directory temporarily
original_cwd = os.getcwd()
try:
    os.chdir(xtquant_path)
    print(f"Changed working directory to {xtquant_path}")
    
    # Check if DLLs can be loaded
    dll_files = ['libeay32.dll', 'ssleay32.dll', 'log4cxx.dll', 'msvcp140.dll', 'vcruntime140.dll']
    for dll in dll_files:
        dll_path = os.path.join(xtquant_path, dll)
        if os.path.exists(dll_path):
            try:
                # Try to load the DLL
                handle = ctypes.windll.kernel32.LoadLibraryW(dll_path)
                if handle:
                    print(f"✓ Successfully loaded {dll}")
                    ctypes.windll.kernel32.FreeLibrary(handle)
                else:
                    print(f"✗ Failed to load {dll}")
            except Exception as e:
                print(f"✗ Error loading {dll}: {e}")
        else:
            print(f"✗ Missing {dll}")
    
    # Now try to import xtquant
    print("\nAttempting to import xtquant from xtquant directory...")
    try:
        from xtquant import xtdata
        print("✓ Successfully imported xtdata!")
        
        # Test a simple function
        print("Testing xtdata functionality...")
        code_list = ['000001.SZ', '600000.SH', '430017.BJ']
        full_tick = xtdata.get_full_tick(code_list)
        print("✓ Successfully called get_full_tick!")
        print(full_tick)
        
    except ImportError as e:
        print(f"✗ ImportError: {e}")
    except Exception as e:
        print(f"✗ Other error: {e}")
        
finally:
    # Restore original working directory
    os.chdir(original_cwd)
    print(f"Restored working directory to {original_cwd}")
