#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Visual C++ Redistributable installer for xtquant
This script downloads and installs the required Visual C++ Redistributable
"""

import os
import sys
import urllib.request
import subprocess
import tempfile

def download_file(url, filename):
    """Download a file from URL"""
    print(f"Downloading {filename}...")
    try:
        urllib.request.urlretrieve(url, filename)
        print(f"✓ Downloaded {filename}")
        return True
    except Exception as e:
        print(f"✗ Failed to download {filename}: {e}")
        return False

def install_vcredist():
    """Download and install Visual C++ Redistributable 2015-2019 x64"""
    
    # URL for Visual C++ Redistributable 2015-2019 x64
    vcredist_url = "https://aka.ms/vs/16/release/vc_redist.x64.exe"
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        vcredist_path = os.path.join(temp_dir, "vc_redist.x64.exe")
        
        # Download the redistributable
        if not download_file(vcredist_url, vcredist_path):
            return False
        
        # Install the redistributable
        print("Installing Visual C++ Redistributable 2015-2019 x64...")
        print("This may require administrator privileges and will open an installer window.")
        
        try:
            # Run the installer
            result = subprocess.run([vcredist_path, "/install", "/quiet", "/norestart"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ Visual C++ Redistributable installed successfully!")
                return True
            elif result.returncode == 1638:
                print("✓ Visual C++ Redistributable is already installed (newer version)!")
                return True
            else:
                print(f"⚠ Installer returned code {result.returncode}")
                print("You may need to run this script as administrator or install manually.")
                print(f"Manual download link: {vcredist_url}")
                return False
                
        except Exception as e:
            print(f"✗ Failed to install: {e}")
            print("Please download and install manually from:")
            print(f"  {vcredist_url}")
            return False

def test_xtquant_after_install():
    """Test xtquant after installing redistributable"""
    print("\n" + "="*50)
    print("Testing xtquant after Visual C++ installation...")
    print("="*50)
    
    try:
        # Add xtquant directory to PATH
        import xtquant
        xtquant_dir = os.path.dirname(xtquant.__file__)
        current_path = os.environ.get('PATH', '')
        if xtquant_dir not in current_path:
            os.environ['PATH'] = xtquant_dir + os.pathsep + current_path
        
        # Add to DLL search path
        if hasattr(os, 'add_dll_directory'):
            try:
                os.add_dll_directory(xtquant_dir)
            except:
                pass
        
        # Try to import xtdata
        from xtquant import xtdata
        print("✓ Successfully imported xtdata!")
        
        # Test functionality
        code_list = ['000001.SZ', '600000.SH', '430017.BJ']
        try:
            full_tick = xtdata.get_full_tick(code_list)
            print("✓ xtquant is working correctly!")
            if full_tick:
                print(f"Sample result: {full_tick}")
            else:
                print("No data returned (normal if market is closed)")
        except Exception as e:
            print(f"⚠ Function test warning: {e}")
            print("This might be normal if market data service is not running")
        
        return True
        
    except ImportError as e:
        print(f"✗ Still getting import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def main():
    print("Visual C++ Redistributable Installer for xtquant")
    print("="*60)
    print("This script will download and install the Visual C++ Redistributable")
    print("2015-2019 x64 which is required for xtquant to work properly.")
    print("="*60)
    
    # Check current status
    try:
        from xtquant import xtdata
        print("✓ xtquant is already working! No installation needed.")
        return
    except ImportError:
        print("xtquant import failed - proceeding with installation...")
    
    # Install Visual C++ Redistributable
    if install_vcredist():
        print("\nInstallation completed. Testing xtquant...")
        if test_xtquant_after_install():
            print("\n🎉 SUCCESS! xtquant is now working!")
        else:
            print("\n❌ xtquant still not working after installation.")
            print("Additional steps you can try:")
            print("1. Restart your computer")
            print("2. Reinstall xtquant package")
            print("3. Check if you have the latest Windows updates")
    else:
        print("\n❌ Failed to install Visual C++ Redistributable.")
        print("Please try installing manually:")
        print("1. Go to: https://aka.ms/vs/16/release/vc_redist.x64.exe")
        print("2. Download and run the installer")
        print("3. Restart your computer")
        print("4. Try running your xtquant script again")

if __name__ == "__main__":
    main()
