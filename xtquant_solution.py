#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
xtquant DLL loading solution
This script provides multiple methods to fix the DLL loading issue
"""

import sys
import os
import shutil

def method1_path_fix():
    """Method 1: Add xtquant directory to PATH and DLL search path"""
    print("Method 1: PATH and DLL directory fix")
    
    try:
        import xtquant
        xtquant_dir = os.path.dirname(xtquant.__file__)
    except ImportError:
        python_path = sys.executable
        site_packages = os.path.join(os.path.dirname(python_path), 'lib', 'site-packages')
        xtquant_dir = os.path.join(site_packages, 'xtquant')
    
    # Add to PATH
    current_path = os.environ.get('PATH', '')
    if xtquant_dir not in current_path:
        os.environ['PATH'] = xtquant_dir + os.pathsep + current_path
    
    # Add to DLL search path
    if hasattr(os, 'add_dll_directory'):
        try:
            os.add_dll_directory(xtquant_dir)
        except:
            pass
    
    try:
        from xtquant import xtdata
        print("✓ Method 1 SUCCESS!")
        return True
    except Exception as e:
        print(f"✗ Method 1 failed: {e}")
        return False

def method2_working_directory():
    """Method 2: Change working directory to xtquant directory"""
    print("Method 2: Working directory change")
    
    try:
        import xtquant
        xtquant_dir = os.path.dirname(xtquant.__file__)
    except ImportError:
        python_path = sys.executable
        site_packages = os.path.join(os.path.dirname(python_path), 'lib', 'site-packages')
        xtquant_dir = os.path.join(site_packages, 'xtquant')
    
    original_cwd = os.getcwd()
    try:
        os.chdir(xtquant_dir)
        from xtquant import xtdata
        print("✓ Method 2 SUCCESS!")
        return True
    except Exception as e:
        print(f"✗ Method 2 failed: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def method3_dll_preload():
    """Method 3: Preload required DLLs"""
    print("Method 3: DLL preloading")
    
    try:
        import xtquant
        xtquant_dir = os.path.dirname(xtquant.__file__)
    except ImportError:
        python_path = sys.executable
        site_packages = os.path.join(os.path.dirname(python_path), 'lib', 'site-packages')
        xtquant_dir = os.path.join(site_packages, 'xtquant')
    
    import ctypes
    
    # Try to preload the DLLs in the correct order
    dll_order = [
        'vcruntime140.dll',
        'msvcp140.dll', 
        'libeay32.dll',
        'ssleay32.dll',
        'log4cxx.dll'
    ]
    
    loaded_dlls = []
    try:
        for dll_name in dll_order:
            dll_path = os.path.join(xtquant_dir, dll_name)
            if os.path.exists(dll_path):
                try:
                    handle = ctypes.windll.kernel32.LoadLibraryW(dll_path)
                    if handle:
                        loaded_dlls.append(handle)
                        print(f"  ✓ Preloaded {dll_name}")
                    else:
                        print(f"  ✗ Failed to preload {dll_name}")
                except Exception as e:
                    print(f"  ✗ Error preloading {dll_name}: {e}")
        
        # Now try to import
        from xtquant import xtdata
        print("✓ Method 3 SUCCESS!")
        return True
        
    except Exception as e:
        print(f"✗ Method 3 failed: {e}")
        return False
    finally:
        # Clean up loaded DLLs
        for handle in loaded_dlls:
            try:
                ctypes.windll.kernel32.FreeLibrary(handle)
            except:
                pass

def method4_system_dll():
    """Method 4: Use system DLLs instead of bundled ones"""
    print("Method 4: System DLL preference")
    
    try:
        import xtquant
        xtquant_dir = os.path.dirname(xtquant.__file__)
    except ImportError:
        python_path = sys.executable
        site_packages = os.path.join(os.path.dirname(python_path), 'lib', 'site-packages')
        xtquant_dir = os.path.join(site_packages, 'xtquant')
    
    # Temporarily rename the bundled Visual C++ runtime DLLs
    # so Python will use the system-installed ones
    bundled_dlls = ['msvcp140.dll', 'vcruntime140.dll']
    renamed_files = []
    
    try:
        for dll_name in bundled_dlls:
            dll_path = os.path.join(xtquant_dir, dll_name)
            if os.path.exists(dll_path):
                backup_path = dll_path + '.backup'
                shutil.move(dll_path, backup_path)
                renamed_files.append((dll_path, backup_path))
                print(f"  ✓ Temporarily renamed {dll_name}")
        
        # Add xtquant dir to PATH for other DLLs
        current_path = os.environ.get('PATH', '')
        if xtquant_dir not in current_path:
            os.environ['PATH'] = xtquant_dir + os.pathsep + current_path
        
        if hasattr(os, 'add_dll_directory'):
            try:
                os.add_dll_directory(xtquant_dir)
            except:
                pass
        
        # Try to import
        from xtquant import xtdata
        print("✓ Method 4 SUCCESS!")
        return True
        
    except Exception as e:
        print(f"✗ Method 4 failed: {e}")
        return False
    finally:
        # Restore the renamed files
        for original_path, backup_path in renamed_files:
            try:
                if os.path.exists(backup_path):
                    shutil.move(backup_path, original_path)
            except:
                pass

def test_xtquant_functionality():
    """Test xtquant functionality once import is successful"""
    try:
        from xtquant import xtdata
        
        print("\n" + "="*50)
        print("Testing xtquant functionality...")
        print("="*50)
        
        # Test basic functionality
        code_list = ['000001.SZ', '600000.SH', '430017.BJ']
        
        try:
            full_tick = xtdata.get_full_tick(code_list)
            print("✓ get_full_tick function works")
            if full_tick:
                print(f"Sample result: {full_tick}")
            else:
                print("No data returned (normal if market is closed)")
        except Exception as e:
            print(f"⚠ get_full_tick error: {e}")
            print("This might be normal if market data service is not running")
        
        print("✓ xtquant is working correctly!")
        
    except Exception as e:
        print(f"✗ Functionality test failed: {e}")

def main():
    print("xtquant DLL Loading Solution")
    print("="*50)
    print(f"Python: {sys.version}")
    print(f"Architecture: {sys.maxsize > 2**32 and '64-bit' or '32-bit'}")
    print("="*50)
    
    methods = [
        method1_path_fix,
        method2_working_directory, 
        method3_dll_preload,
        method4_system_dll
    ]
    
    for i, method in enumerate(methods, 1):
        print(f"\nTrying method {i}...")
        try:
            if method():
                print(f"\n🎉 SUCCESS with Method {i}!")
                test_xtquant_functionality()
                return
        except Exception as e:
            print(f"Method {i} crashed: {e}")
        
        # Clear any imported modules to try fresh
        modules_to_remove = [name for name in sys.modules.keys() if 'xtquant' in name or 'xtdata' in name]
        for module_name in modules_to_remove:
            del sys.modules[module_name]
    
    print("\n" + "="*50)
    print("❌ All methods failed!")
    print("Please try the following:")
    print("1. Download and install Visual C++ Redistributable 2015-2019 x64")
    print("2. Restart your computer")
    print("3. Reinstall xtquant package")
    print("="*50)

if __name__ == "__main__":
    main()
